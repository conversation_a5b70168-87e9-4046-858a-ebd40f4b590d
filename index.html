<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日出滚动动画演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 50px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 40px;
            margin-bottom: 60px;
        }

        .demo-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .demo-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 60px rgba(0,0,0,0.15);
        }

        .demo-card h2 {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .demo-card p {
            margin-bottom: 1.5rem;
            color: #666;
            font-size: 1.1rem;
        }

        .demo-preview {
            width: 100%;
            height: 200px;
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
            position: relative;
            background: linear-gradient(45deg, #ff6b35, #f7931e, #ffcc5c, #87ceeb);
            background-size: 400% 400%;
            animation: gradientShift 4s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .demo-preview::after {
            content: '预览';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .demo-button {
            display: inline-block;
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: bold;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(255, 107, 53, 0.3);
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
        }

        .features {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            backdrop-filter: blur(10px);
        }

        .features h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            color: #333;
        }

        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }

        .feature-item {
            text-align: center;
            padding: 20px;
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .feature-item h3 {
            font-size: 1.3rem;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .feature-item p {
            color: #666;
        }

        .tech-info {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            backdrop-filter: blur(10px);
        }

        .tech-info h2 {
            font-size: 2rem;
            margin-bottom: 1.5rem;
            color: #333;
        }

        .tech-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .tech-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #ff6b35;
        }

        .tech-item strong {
            color: #ff6b35;
        }

        .footer {
            text-align: center;
            margin-top: 60px;
            color: white;
            opacity: 0.8;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
            
            .container {
                padding: 30px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌅 日出滚动动画演示</h1>
            <p>体验两种不同技术实现的滚动驱动日出动画效果，灵感来源于现代网站的视觉叙事技术</p>
        </div>

        <div class="demo-grid">
            <div class="demo-card">
                <h2>🎨 CSS + JavaScript 版本</h2>
                <div class="demo-preview"></div>
                <p>使用CSS渐变和JavaScript实现的平滑日出动画。通过CSS变量和滚动监听，创造出流畅的颜色过渡和元素移动效果。</p>
                <p><strong>特点：</strong></p>
                <ul style="margin: 15px 0; padding-left: 20px; color: #666;">
                    <li>轻量级实现</li>
                    <li>平滑的颜色过渡</li>
                    <li>响应式设计</li>
                    <li>优秀的性能表现</li>
                </ul>
                <a href="sunrise-scroll-animation.html" class="demo-button">查看演示</a>
            </div>

            <div class="demo-card">
                <h2>🖼️ Canvas 帧动画版本</h2>
                <div class="demo-preview"></div>
                <p>使用Canvas技术生成连续帧的高级动画实现。每一帧都是程序生成的完整场景，提供更精细的视觉控制。</p>
                <p><strong>特点：</strong></p>
                <ul style="margin: 15px 0; padding-left: 20px; color: #666;">
                    <li>高精度帧控制</li>
                    <li>复杂场景渲染</li>
                    <li>自动播放功能</li>
                    <li>实时帧信息显示</li>
                </ul>
                <a href="sunrise-frame-animation.html" class="demo-button">查看演示</a>
            </div>
        </div>

        <div class="features">
            <h2>✨ 核心特性</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <div class="feature-icon">📱</div>
                    <h3>响应式设计</h3>
                    <p>完美适配桌面和移动设备，提供一致的用户体验</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">⚡</div>
                    <h3>性能优化</h3>
                    <p>使用requestAnimationFrame和事件节流，确保流畅运行</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🎮</div>
                    <h3>交互控制</h3>
                    <p>支持滚动控制、自动播放、重置等多种交互方式</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🌈</div>
                    <h3>视觉效果</h3>
                    <p>精美的颜色过渡和动画效果，营造沉浸式体验</p>
                </div>
            </div>
        </div>

        <div class="tech-info">
            <h2>🛠️ 技术实现</h2>
            <p>这些演示展示了现代Web技术在创建引人入胜的滚动动画方面的强大能力。类似的技术被广泛应用于品牌网站、产品展示和数字叙事中。</p>
            
            <div class="tech-list">
                <div class="tech-item">
                    <strong>HTML5 Canvas</strong><br>
                    用于复杂图形渲染和帧动画
                </div>
                <div class="tech-item">
                    <strong>CSS Variables</strong><br>
                    动态控制样式属性
                </div>
                <div class="tech-item">
                    <strong>Scroll Events</strong><br>
                    监听滚动位置变化
                </div>
                <div class="tech-item">
                    <strong>RequestAnimationFrame</strong><br>
                    优化动画性能
                </div>
                <div class="tech-item">
                    <strong>CSS Gradients</strong><br>
                    创建平滑的颜色过渡
                </div>
                <div class="tech-item">
                    <strong>Responsive Design</strong><br>
                    适配不同设备尺寸
                </div>
            </div>

            <h3 style="margin-top: 30px; color: #333;">应用场景</h3>
            <p style="margin-top: 15px; color: #666;">
                这种滚动驱动的动画技术常用于：品牌故事叙述、产品功能展示、数据可视化、艺术作品展示、教育内容呈现等场景。
                通过将静态内容转化为动态体验，能够显著提升用户参与度和记忆度。
            </p>
        </div>

        <div class="footer">
            <p>💡 灵感来源于现代网站设计趋势，如 adaline.ai 等优秀案例</p>
            <p style="margin-top: 10px; font-size: 0.9rem;">使用现代Web技术实现，兼容主流浏览器</p>
        </div>
    </div>
</body>
</html>
