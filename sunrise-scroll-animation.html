<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日出滚动动画</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            overflow-x: hidden;
        }

        /* 滚动容器 */
        .scroll-container {
            height: 500vh; /* 5倍视口高度，提供足够的滚动空间 */
            position: relative;
        }

        /* 固定的动画容器 */
        .animation-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            overflow: hidden;
            z-index: 1;
        }

        /* 天空背景 */
        .sky {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                to bottom,
                var(--sky-top, #0a0a2e),
                var(--sky-middle, #16213e),
                var(--sky-bottom, #1a1a3a)
            );
            transition: all 0.1s ease-out;
        }

        /* 地面 */
        .ground {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 30%;
            background: linear-gradient(
                to bottom,
                var(--ground-top, #2d4a22),
                var(--ground-bottom, #1a2e15)
            );
            transition: all 0.1s ease-out;
        }

        /* 太阳 */
        .sun {
            position: absolute;
            width: 120px;
            height: 120px;
            background: radial-gradient(
                circle,
                var(--sun-center, #ffd700),
                var(--sun-edge, #ffaa00)
            );
            border-radius: 50%;
            left: 50%;
            transform: translateX(-50%) translateY(var(--sun-y, 100vh));
            box-shadow: 
                0 0 50px var(--sun-glow, rgba(255, 215, 0, 0.3)),
                0 0 100px var(--sun-glow, rgba(255, 215, 0, 0.2)),
                0 0 150px var(--sun-glow, rgba(255, 215, 0, 0.1));
            transition: all 0.1s ease-out;
        }

        /* 云朵 */
        .cloud {
            position: absolute;
            background: var(--cloud-color, rgba(255, 255, 255, 0.1));
            border-radius: 50px;
            opacity: var(--cloud-opacity, 0.3);
            transition: all 0.2s ease-out;
        }

        .cloud1 {
            width: 100px;
            height: 40px;
            top: 20%;
            left: 20%;
            animation: float1 20s infinite ease-in-out;
        }

        .cloud2 {
            width: 80px;
            height: 30px;
            top: 15%;
            right: 25%;
            animation: float2 25s infinite ease-in-out;
        }

        .cloud3 {
            width: 120px;
            height: 45px;
            top: 30%;
            left: 60%;
            animation: float3 30s infinite ease-in-out;
        }

        /* 云朵飘动动画 */
        @keyframes float1 {
            0%, 100% { transform: translateX(0) translateY(0); }
            50% { transform: translateX(20px) translateY(-10px); }
        }

        @keyframes float2 {
            0%, 100% { transform: translateX(0) translateY(0); }
            50% { transform: translateX(-15px) translateY(5px); }
        }

        @keyframes float3 {
            0%, 100% { transform: translateX(0) translateY(0); }
            50% { transform: translateX(10px) translateY(-5px); }
        }

        /* 内容区域 */
        .content {
            position: relative;
            z-index: 2;
            background: rgba(255, 255, 255, 0.95);
            margin-top: 100vh;
            padding: 50px;
            min-height: 100vh;
        }

        .content h1 {
            font-size: 3rem;
            margin-bottom: 2rem;
            color: #333;
            text-align: center;
        }

        .content p {
            font-size: 1.2rem;
            line-height: 1.8;
            color: #666;
            max-width: 800px;
            margin: 0 auto 2rem;
        }

        /* 进度指示器 */
        .progress-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 3;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sun {
                width: 80px;
                height: 80px;
            }
            
            .content h1 {
                font-size: 2rem;
            }
            
            .content p {
                font-size: 1rem;
                padding: 0 20px;
            }
        }
    </style>
</head>
<body>
    <div class="scroll-container">
        <!-- 动画容器 -->
        <div class="animation-container">
            <!-- 天空 -->
            <div class="sky"></div>
            
            <!-- 云朵 -->
            <div class="cloud cloud1"></div>
            <div class="cloud cloud2"></div>
            <div class="cloud cloud3"></div>
            
            <!-- 太阳 -->
            <div class="sun"></div>
            
            <!-- 地面 -->
            <div class="ground"></div>
        </div>

        <!-- 进度指示器 -->
        <div class="progress-indicator">
            滚动进度: <span id="progress">0%</span>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <h1>日出滚动动画演示</h1>
            <p>这是一个基于滚动的日出动画演示。当你向下滚动页面时，可以看到太阳从地平线升起，天空颜色从深夜逐渐变为白天。</p>
            <p>动画包含了多个阶段：深夜、黎明、日出、上午和白天。每个阶段都有不同的颜色搭配和太阳位置。</p>
            <p>这种技术常用于现代网站的视觉叙事，通过滚动交互来展示故事或产品特性。</p>
            <p>继续滚动可以体验完整的日出过程...</p>
        </div>
    </div>

    <script>
        // 获取DOM元素
        const root = document.documentElement;
        const progressElement = document.getElementById('progress');
        
        // 动画配置
        const animationStages = {
            // 深夜 (0-20%)
            night: {
                skyTop: '#0a0a2e',
                skyMiddle: '#16213e', 
                skyBottom: '#1a1a3a',
                groundTop: '#1a2e15',
                groundBottom: '#0f1a0c',
                sunY: '100vh',
                sunCenter: '#ffd700',
                sunEdge: '#ffaa00',
                sunGlow: 'rgba(255, 215, 0, 0.1)',
                cloudColor: 'rgba(255, 255, 255, 0.05)',
                cloudOpacity: 0.2
            },
            // 黎明 (20-40%)
            dawn: {
                skyTop: '#1a1a3a',
                skyMiddle: '#2d4a5a',
                skyBottom: '#4a5d6a',
                groundTop: '#2d4a22',
                groundBottom: '#1a2e15',
                sunY: '80vh',
                sunCenter: '#ffb347',
                sunEdge: '#ff8c42',
                sunGlow: 'rgba(255, 179, 71, 0.3)',
                cloudColor: 'rgba(255, 255, 255, 0.1)',
                cloudOpacity: 0.3
            },
            // 日出 (40-60%)
            sunrise: {
                skyTop: '#ff6b35',
                skyMiddle: '#f7931e',
                skyBottom: '#ffcc5c',
                groundTop: '#4a6741',
                groundBottom: '#2d4a22',
                sunY: '60vh',
                sunCenter: '#ff6b35',
                sunEdge: '#ff4500',
                sunGlow: 'rgba(255, 107, 53, 0.5)',
                cloudColor: 'rgba(255, 255, 255, 0.2)',
                cloudOpacity: 0.4
            },
            // 上午 (60-80%)
            morning: {
                skyTop: '#87ceeb',
                skyMiddle: '#98d8e8',
                skyBottom: '#b8e6f0',
                groundTop: '#6b8e5a',
                groundBottom: '#4a6741',
                sunY: '40vh',
                sunCenter: '#ffd700',
                sunEdge: '#ffaa00',
                sunGlow: 'rgba(255, 215, 0, 0.4)',
                cloudColor: 'rgba(255, 255, 255, 0.6)',
                cloudOpacity: 0.6
            },
            // 白天 (80-100%)
            day: {
                skyTop: '#87ceeb',
                skyMiddle: '#b0e0e6',
                skyBottom: '#e0f6ff',
                groundTop: '#8fbc8f',
                groundBottom: '#6b8e5a',
                sunY: '20vh',
                sunCenter: '#ffd700',
                sunEdge: '#ffaa00',
                sunGlow: 'rgba(255, 215, 0, 0.3)',
                cloudColor: 'rgba(255, 255, 255, 0.8)',
                cloudOpacity: 0.7
            }
        };

        // 插值函数
        function lerp(start, end, factor) {
            return start + (end - start) * factor;
        }

        // 颜色插值函数
        function lerpColor(color1, color2, factor) {
            // 简化的颜色插值，实际应用中可以使用更复杂的颜色空间转换
            return color2; // 这里简化处理，直接返回目标颜色
        }

        // 获取当前动画阶段
        function getCurrentStage(progress) {
            if (progress < 0.2) return { current: 'night', next: 'dawn', factor: progress / 0.2 };
            if (progress < 0.4) return { current: 'dawn', next: 'sunrise', factor: (progress - 0.2) / 0.2 };
            if (progress < 0.6) return { current: 'sunrise', next: 'morning', factor: (progress - 0.4) / 0.2 };
            if (progress < 0.8) return { current: 'morning', next: 'day', factor: (progress - 0.6) / 0.2 };
            return { current: 'day', next: 'day', factor: 1 };
        }

        // 更新动画
        function updateAnimation(progress) {
            const stage = getCurrentStage(progress);
            const currentStage = animationStages[stage.current];
            const nextStage = animationStages[stage.next];
            
            // 更新CSS变量
            root.style.setProperty('--sky-top', stage.factor < 1 ? currentStage.skyTop : nextStage.skyTop);
            root.style.setProperty('--sky-middle', stage.factor < 1 ? currentStage.skyMiddle : nextStage.skyMiddle);
            root.style.setProperty('--sky-bottom', stage.factor < 1 ? currentStage.skyBottom : nextStage.skyBottom);
            root.style.setProperty('--ground-top', stage.factor < 1 ? currentStage.groundTop : nextStage.groundTop);
            root.style.setProperty('--ground-bottom', stage.factor < 1 ? currentStage.groundBottom : nextStage.groundBottom);
            root.style.setProperty('--sun-y', stage.factor < 1 ? currentStage.sunY : nextStage.sunY);
            root.style.setProperty('--sun-center', stage.factor < 1 ? currentStage.sunCenter : nextStage.sunCenter);
            root.style.setProperty('--sun-edge', stage.factor < 1 ? currentStage.sunEdge : nextStage.sunEdge);
            root.style.setProperty('--sun-glow', stage.factor < 1 ? currentStage.sunGlow : nextStage.sunGlow);
            root.style.setProperty('--cloud-color', stage.factor < 1 ? currentStage.cloudColor : nextStage.cloudColor);
            root.style.setProperty('--cloud-opacity', stage.factor < 1 ? currentStage.cloudOpacity : nextStage.cloudOpacity);
            
            // 更新进度显示
            progressElement.textContent = Math.round(progress * 100) + '%';
        }

        // 滚动事件处理
        let ticking = false;
        
        function handleScroll() {
            if (!ticking) {
                requestAnimationFrame(() => {
                    const scrollTop = window.pageYOffset;
                    const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
                    const progress = Math.min(scrollTop / documentHeight, 1);
                    
                    updateAnimation(progress);
                    ticking = false;
                });
                ticking = true;
            }
        }

        // 初始化
        window.addEventListener('scroll', handleScroll);
        updateAnimation(0); // 设置初始状态
        
        // 平滑滚动到顶部的函数（可选）
        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
        
        // 添加键盘快捷键（可选）
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Home') {
                e.preventDefault();
                scrollToTop();
            }
        });
    </script>
</body>
</html>
